# Welcome to your RPG!
# Chapter 1: The Journey Begins with Decisions
import random

# --- Function Definitions ---
def start_battle(player_hp, inv):
    #This function contains all the logic for a single battle.
    monster_name = "Goblin"
    monster_health = 50
    print(f"\n--- A wild {monster_name} appears! ---")

    #The battle loop is now INSIDE a function
    #REMEMBER!!!! How to do RPG Battles
    while True:
        print(f"\nYour Health: {player_hp} | {monster_name}'s Health: {monster_health}")
        action = input("What do you do? (Enter 'attack' or 'run'): ")

        if action == "attack":
            player_damage = random.randint(10, 20)  # random number between 10 and 20
            monster_health -= player_damage
            print(f"You attack the {monster_name} for {player_damage} damage!")

            if monster_health <= 0:
                print(f"You have defeated the {monster_name}!")
                print("You find a small bright red jewel. You pick it up.");
                inv.append("ruby")
                break
        #Run away from battle
        elif action == "run":
            print("You decided to run away. The battle is over.")
            break

        else:
            print("Invalid action! You hesitate and do nothing.")

        monster_damage = random.randint(5, 15)
        player_hp -= monster_damage
        print(f"The {monster_name} attacks you for {monster_damage} damage!")

        if player_hp <= 0:
            print("You have been defeated...")
            break

    return player_hp, in


#Visiting the Village
#Simple Function call REMEMBER!!!!!!
def visit_village(player_hp):
    print("\nYou decide to visit the nearby village. The villagers are friendly and offer you a place to rest and recover.")
    player_hp = 100  # Full health in the village
    return player_hp





# --- Main Story ---
player_name = input("What is your name, brave adventurer? ")
print(f"Welcome, {player_name}! Your journey is about to begin.")

# --- The First Choice ---
print("\nYou stand at a crossroads. A sign points in three different directions. Two paths are clearly marked, while the third is just a faint whisper in the wind. Choose your path wisely, for your choices will shape your destiny.")
print("1. Follow the well-trodden path into the dense forest.")
print("2. Venture down the path less traveled, leading to a serene meadow.")
print("3. Listen to the faint whisper and follow the sound.")

choice = input("Enter your choice (1/2/3): ")

if choice == "1":
    print("\nYou decide to follow the well-trodden path into the dense forest. The trees are tall and the canopy is thick, allowing only a muted sunlight to filter through. You hear the rustle of leaves and the chirp of birds, but also the distant growl of something primal.")    
    player_hp = 100  # Starting health
    player_hp = start_battle(player_hp)  # Call the battle function
    if player_hp <= 0:
        print("Your journey ends here...")
    else:
        print("You emerge victorious from the battle. Your journey continues...")
    if player_hp <= 0:
        print("Your journey ends here...")
    else:
        print("You emerge victorious from the battle. Your journey continues...")
        # Add more story content here for the forest path

elif choice == "2": 
    print("\nYou decide to venture down the path less traveled, leading to a serene meadow. The grass is lush and green, and the air is filled with the scent of wildflowers. You hear the gentle hum of bees and the chirp of birds, but also the distant growl of something primal.")    
   
        # Add more story content here for the meadow path

elif choice == "3":
    print("\nYou decide to listen to the faint whisper and follow the sound. The path is narrow and treacherous, but you persevere. The sound grows louder and louder until you reach a clearing. In the center of the clearing, you see a wise old owl perched on a stump. The owl nods to you and says, 'Welcome, traveler. I have been waiting for you.'")    
    # Add more story content here for the owl encounter

else:
    print("Invalid choice. Your indecision bewilders the forces of fate, and you are lost in a timeless limbo. The end.")
    